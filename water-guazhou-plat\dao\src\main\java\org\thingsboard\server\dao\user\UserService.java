/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.user;

import com.google.common.util.concurrent.ListenableFuture;
import org.thingsboard.server.common.data.Tenant;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.CustomerId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.common.data.page.TextPageData;
import org.thingsboard.server.common.data.page.TextPageLink;
import org.thingsboard.server.common.data.security.UserCredentials;
import org.thingsboard.server.dao.model.sql.UserWithPassword;

import java.util.List;

public interface UserService {

	User findUserById(TenantId tenantId, UserId userId);

	User findUserById( UserId userId);

	ListenableFuture<User> findUserByIdAsync(TenantId tenantId, UserId userId);

	User findUserByEmail(TenantId tenantId, String email);

	User findUserByEmail(String email);

	User saveUser(User user) throws ThingsboardException;

	UserCredentials findUserCredentialsByUserId(TenantId tenantId, UserId userId);

	UserCredentials findUserCredentialsByUserId(UserId userId);
	
	UserCredentials findUserCredentialsByActivateToken(TenantId tenantId, String activateToken);

	UserCredentials findUserCredentialsByResetToken(TenantId tenantId, String resetToken);

	UserCredentials saveUserCredentials(TenantId tenantId, UserCredentials userCredentials);
	
	UserCredentials activateUserCredentials(TenantId tenantId, String activateToken, String password);
	
	UserCredentials requestPasswordReset(TenantId tenantId, String email);

	void deleteUser(TenantId tenantId, UserId userId);
	
	TextPageData<User> findTenantAdmins(TenantId tenantId, TextPageLink pageLink);
	
	void deleteTenantAdmins(TenantId tenantId);
	
	TextPageData<User> findCustomerUsers(TenantId tenantId, CustomerId customerId, TextPageLink pageLink);
	    
	void deleteCustomerUsers(TenantId tenantId, CustomerId customerId);

	List<User> findUserByTenant(TenantId tenantId);

	TextPageData<User> findTenantUsers(TenantId tenantId, TextPageLink pageLink);

	TextPageData<User> findTenantSysAndAdmin(TenantId tenantId, TextPageLink pageLink);

    void assignUserToProjects(List<String> projectIds, String userId);

    long getAllUserCount();

    List<User> findAll();


    void changeStatus(String userId, String f);

	List<Tenant> findUserTenantByUserId(String userId);

    PageData<User> findList(Integer page, Integer size, String title, String authority, String tenantId);

    User findUserBySerialNo(String serialNo);

    List<User> findByIdIn(List<String> ids);

    void updateUser(User user);

	PageData<User> findAllByPid(String pid, String name, String roleId,String tenantId, Boolean status, int page, int size);

    List getAllByPidStr(String pid, String tenantId);

	List getAllUser(String name, String tenantId);

    User findByAccountKey(String accountKey);

    List<User> findListByAuth(String authType, User currentUser);

	List<UserWithPassword> getAllUsers();
}
