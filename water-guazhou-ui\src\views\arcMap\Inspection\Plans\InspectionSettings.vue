<template>
  <div class="inspection-settings">
    <Search
      ref="refSearch"
      :config="SearchConfig"
      style="margin-bottom: 8px; padding: 12px; background-color: #fff; border-radius: 4px;"
    ></Search>
    <div class="table-box">
      <FormTable :config="TableConfig"></FormTable>
    </div>

    <!-- 新建/编辑弹窗 -->
    <CircuitSettingsDialog
      v-model="dialogVisible"
      :edit-id="editId"
      :readonly="isViewMode"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, shallowRef } from 'vue'
import { Edit, Delete, View } from '@element-plus/icons-vue'
import Search from '@/components/Form/Search.vue'
import FormTable from '@/components/Form/FormTable.vue'
import CircuitSettingsDialog from './components/CircuitSettingsDialog.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDate } from '@/utils/DateFormatter'
import {
  getCircuitSettingsList,
  deleteCircuitSettings
} from '@/api/smartManagement/circuitSettings'

// 搜索配置
const SearchConfig = reactive<ISearch>({
  labelWidth: 80,
  filters: [
    {
      type: 'input',
      label: '配置名称',
      field: 'name'
    },
    {
      type: 'input',
      label: '配置编码',
      field: 'code'
    },
    {
      type: 'select',
      label: '配置类型',
      field: 'type',
      options: [
        { label: '管网', value: '0' },
        { label: '泵站', value: '1' },
        { label: '其他', value: '2' }
      ]
    },
    {
      type: 'select',
      label: '状态',
      field: 'status',
      options: [
        { label: '启用', value: '0' },
        { label: '停用', value: '1' }
      ]
    },
    {
      type: 'daterange',
      label: '创建时间',
      field: 'createTime',
      format: 'YYYY-MM-DD'
    },
    {
      type: 'btn-group',
      btns: [
        {
          text: '查询',
          type: 'primary',
          click: () => refreshData()
        },
        {
          text: '重置',
          type: 'default',
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        }
      ]
    }
  ]
})

// 表格配置
const TableConfig = reactive<ITable>({
  title: '培训配置列表',
  height: 'calc(100vh - 180px)',
  indexVisible: true,
  columns: [
    { minWidth: 120, label: '配置名称', prop: 'name' },
    { minWidth: 120, label: '配置编码', prop: 'code' },
    {
      minWidth: 120,
      label: '配置类型',
      prop: 'type',
      formatter: (row: any) => {
        const typeMap: Record<string, string> = {
          '0': '管网',
          '1': '泵站',
          '2': '其他'
        }
        return typeMap[row.type] || row.type
      }
    },
    {
      minWidth: 160,
      label: '创建时间',
      prop: 'createTime',
      formatter: (row: any) => formatDate(row.createTime, 'YYYY-MM-DD HH:mm:ss')
    },
    {
      minWidth: 100,
      label: '状态',
      prop: 'status',
      formatter: (row: any) => row.status === '0' ? '启用' : '停用'
    }
  ],
  dataList: [],
  loading: false,
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }: { page: number; size: number }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  operations: [
    {
      text: '查看',
      isTextBtn: true,
      svgIcon: shallowRef(View),
      click: (row: any) => handleView(row)
    },
    {
      text: '编辑',
      isTextBtn: true,
      svgIcon: shallowRef(Edit),
      click: (row: any) => handleEdit(row)
    },
    {
      text: '删除',
      isTextBtn: true,
      svgIcon: shallowRef(Delete),
      click: (row: any) => handleDelete(row)
    }
  ],
  titleRight: [
    {
      style: {
        justifyContent: 'flex-end'
      },
      items: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '新建表单',
              type: 'primary',
              click: () => handleCreate()
            }
          ]
        }
      ]
    }
  ]
})

const refSearch = ref<ISearchIns>()

// 弹窗相关
const dialogVisible = ref(false)
const editId = ref('')
const isViewMode = ref(false)

// 刷新数据
const refreshData = async () => {
  try {
    TableConfig.loading = true
    const queryParams = refSearch.value?.queryParams || {}

    // 处理日期范围参数
    let fromTime = ''
    let toTime = ''
    if (queryParams.createTime && Array.isArray(queryParams.createTime)) {
      fromTime = queryParams.createTime[0]
      toTime = queryParams.createTime[1]
    }

    const params = {
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20,
      name: queryParams.name,
      code: queryParams.code,
      type: queryParams.type,
      status: queryParams.status,
      fromTime,
      toTime
    }

    const res = await getCircuitSettingsList(params)
    if (res?.data) {
      TableConfig.dataList = res.data.records || []
      TableConfig.pagination.total = res.data.total || 0
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  } finally {
    TableConfig.loading = false
  }
}

// 查看详情
const handleView = (row: any) => {
  editId.value = row.id
  isViewMode.value = true
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row: any) => {
  editId.value = row.id
  isViewMode.value = false
  dialogVisible.value = true
}

// 删除
const handleDelete = (row: any) => {
  ElMessageBox.confirm('确定要删除该培训配置吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteCircuitSettings(row.id)
      ElMessage.success('删除成功')
      refreshData()
    } catch (error) {
      console.error(error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}

// 新建
const handleCreate = () => {
  editId.value = ''
  isViewMode.value = false
  dialogVisible.value = true
}

// 弹窗成功回调
const handleDialogSuccess = () => {
  refreshData()
}

onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.inspection-settings {
  height: 100%;
  padding: 16px;
  background-color: #fff;
  
  .search-area {
    margin-bottom: 16px;
  }
  
  .table-box {
    background-color: #fff;
    border-radius: 4px;
  }
}
</style>