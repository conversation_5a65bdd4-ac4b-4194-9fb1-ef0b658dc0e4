<template>
  <div class="workflow-test">
    <div class="test-header">
      <h2>工作流系统连接测试</h2>
      <div class="test-controls">
        <button @click="testConnection" class="test-btn" :disabled="testing">
          {{ testing ? '测试中...' : '测试连接' }}
        </button>
        <button @click="getAuthUrl" class="test-btn" :disabled="testing">
          获取认证URL
        </button>
        <button @click="openWorkflow" class="test-btn success" :disabled="!authUrl">
          打开工作流
        </button>
      </div>
    </div>

    <div class="test-results">
      <div class="result-section">
        <h3>连接测试结果</h3>
        <div v-if="testResult" class="result-box" :class="testResult.status">
          <pre>{{ JSON.stringify(testResult, null, 2) }}</pre>
        </div>
        <div v-else class="result-box empty">
          点击"测试连接"按钮开始测试
        </div>
      </div>

      <div class="result-section">
        <h3>认证URL</h3>
        <div v-if="authResult" class="result-box" :class="authResult.status">
          <div><strong>URL:</strong> {{ authResult.url }}</div>
          <div><strong>方法:</strong> {{ authResult.method }}</div>
          <div><strong>状态:</strong> {{ authResult.status }}</div>
        </div>
        <div v-else class="result-box empty">
          点击"获取认证URL"按钮开始获取
        </div>
      </div>
    </div>

    <div class="iframe-section" v-if="showIframe">
      <h3>工作流系统预览</h3>
      <iframe 
        :src="authUrl" 
        class="workflow-iframe"
        @load="onIframeLoad"
      ></iframe>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WorkflowTest',
  data() {
    return {
      testing: false,
      testResult: null,
      authResult: null,
      authUrl: '',
      showIframe: false
    }
  },
  methods: {
    async testConnection() {
      this.testing = true;
      this.testResult = null;
      
      try {
        console.log('开始测试连接...');
        const response = await fetch('/api/workflow-auth/test');
        const result = await response.json();
        
        this.testResult = result;
        console.log('测试结果:', result);
        
      } catch (error) {
        this.testResult = {
          status: 'error',
          error: error.message
        };
        console.error('测试失败:', error);
      } finally {
        this.testing = false;
      }
    },

    async getAuthUrl() {
      this.testing = true;
      this.authResult = null;
      
      try {
        console.log('获取认证URL...');
        const response = await fetch('/api/workflow-auth/getAuthUrl');
        const result = await response.json();
        
        this.authResult = result;
        if (result.url) {
          this.authUrl = result.url;
        }
        console.log('认证URL结果:', result);
        
      } catch (error) {
        this.authResult = {
          status: 'error',
          error: error.message
        };
        console.error('获取认证URL失败:', error);
      } finally {
        this.testing = false;
      }
    },

    openWorkflow() {
      if (this.authUrl) {
        this.showIframe = true;
        console.log('打开工作流系统:', this.authUrl);
      }
    },

    onIframeLoad() {
      console.log('工作流iframe加载完成');
    }
  },

  mounted() {
    // 自动开始测试
    this.testConnection();
    this.getAuthUrl();
  }
}
</script>

<style scoped>
.workflow-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #eee;
}

.test-header h2 {
  margin: 0;
  color: #2c3e50;
}

.test-controls {
  display: flex;
  gap: 10px;
}

.test-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease;
}

.test-btn:not(.success) {
  background: #3498db;
  color: white;
}

.test-btn.success {
  background: #2ecc71;
  color: white;
}

.test-btn:hover:not(:disabled) {
  opacity: 0.8;
}

.test-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.test-results {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.result-section h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
}

.result-box {
  padding: 15px;
  border-radius: 5px;
  border: 1px solid #ddd;
  min-height: 100px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.result-box.success {
  background: #d5f4e6;
  border-color: #2ecc71;
}

.result-box.error {
  background: #fdf2f2;
  border-color: #e74c3c;
}

.result-box.proxy {
  background: #fff3cd;
  border-color: #f39c12;
}

.result-box.empty {
  background: #f8f9fa;
  color: #6c757d;
  font-style: italic;
  display: flex;
  align-items: center;
  justify-content: center;
}

.iframe-section {
  margin-top: 30px;
}

.iframe-section h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
}

.workflow-iframe {
  width: 100%;
  height: 600px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
