package org.thingsboard.server.dao.sql.user;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.UserEntity;
import org.thingsboard.server.dao.model.sql.UserWithPassword;

import java.util.List;

@Mapper
public interface UserWithPasswordMapper extends BaseMapper<UserEntity> {


    List<UserWithPassword> getAllWithPassword();
}
