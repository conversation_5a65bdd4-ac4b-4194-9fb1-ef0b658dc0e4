<template>
  <div class="workflow-container">
    <!-- 头部信息 -->
    <div class="header-info">
      <h2>工作流系统</h2>
      <div class="connection-status">
        <span :class="['status-indicator', connectionStatus]"></span>
        <span class="status-text">{{ getStatusText() }}</span>
      </div>
    </div>

    <!-- 加载状态 -->
    <!-- <div v-if="loading" class="loading-container" v-loading="loading" element-loading-text="正在加载工作流系统...">
      <div style="height: 200px;"></div>
    </div> -->
    <iframe 
      ref="workflowFrame"
      :src="iframeSrc"
      :onload="onloadFrame"
      class="embedded-frame"
      allow="fullscreen"
      frameborder="0"
    ></iframe>

    <!-- 认证信息显示 -->
    <!-- <div v-if="authMethod" class="auth-info">
      认证方式: {{ authMethod }}
    </div> -->

    <!-- 系统水印 -->
    <!-- <div class="system-watermark">嵌入式工作流系统 v1.0</div> -->

    <!-- 调试信息 -->
    <!-- <div v-if="!loading && !error" class="debug-info">
      <p><strong>目标URL:</strong> {{ baseUrl }}</p>
      <p><strong>认证状态:</strong> {{ connectionStatus }}</p>
      <p><strong>Token前缀:</strong> {{ authToken.substring(0, 50) }}...</p>
      <p><strong>ClientID:</strong> {{ clientId }}</p>
    </div> -->

    <!-- 嵌入的iframe -->
    <!-- <iframe
      ref="workflowFrame"
      :src="iframeSrc"
      :onload="onloadFrame"
      class="embedded-frame"
      allow="fullscreen"
      frameborder="0"
    ></iframe> -->
  </div>
</template>

<script>
export default {
  name: 'TestFlow',
  data() {
    return {
      loading: true,
      error: null,
      connectionStatus: 'connecting', // connecting, connected, error
      authMethod: 'Direct Access + Auth Injection',
      authUrl: '',
      baseUrl: 'http://**********:5666/workflow/processDefinition',
      clientId: 'e5cd7e4891bf95d1d19206ce24a7b32e',
      loadingTimeout: null
    }
  },
  computed: {
    iframeSrc() {
      // 直接访问工作流系统
      return this.baseUrl;
    }
  },
  mounted() {
    // this.initializeWorkflow();

  },
  methods: {
    onloadFrame(){
      const message = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI4eTQ4UXdxRDJqcWpESlQySVF1aHB5RHpEVHFUQlFTOCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuawtOWKoeWxgCIsImRlcHRDYXRlZ29yeSI6IiJ9.xhUc_NU6l8K0kSbAJwVr6_MFApV2_E9Q0pvfcpYeNv8';

      alert('尝试向iframe发送消息:');
      const iframe = this.$refs.workflowFrame;
      iframe.contentWindow.postMessage(message, '*');
    },
    async initializeWorkflow() {
      try {
        this.loading = true;
        this.error = null;
        this.connectionStatus = 'connecting';

        // 设置超时，如果10秒内没有加载完成就显示错误
        this.loadingTimeout = setTimeout(() => {
          // if (this.loading) {
          //   console.error('iframe加载超时');
          //   this.error = 'iframe加载超时，可能存在跨域问题或网络连接问题';
          //   this.connectionStatus = 'error';
          //   this.loading = false;
          // }
          this.onIframeLoad();
        }, 1000);

      } catch (error) {
        console.error('初始化工作流失败:', error);
        this.error = '无法连接到工作流系统，请检查网络连接';
        this.connectionStatus = 'error';
        this.loading = false;
      }
    },

    onIframeLoad() {
      console.log('iframe 加载完成');
      console.log('工作流系统已成功加载:', this.baseUrl);

      // 清除超时定时器
      if (this.loadingTimeout) {
        clearTimeout(this.loadingTimeout);
        this.loadingTimeout = null;
      }

      // 直接设置为已连接状态
      this.loading = false;
      this.connectionStatus = 'connected';
      console.log('工作流系统连接成功');

      // 使用$nextTick确保DOM已更新，然后注入认证信息
      this.$nextTick(() => {
        console.log('DOM已更新，准备注入认证信息...');
        this.injectAuthToIframe();
      });
    },

    onIframeError(event) {
      alert(11)
      console.error('iframe 加载失败', event);

      // 清除超时定时器
      if (this.loadingTimeout) {
        clearTimeout(this.loadingTimeout);
        this.loadingTimeout = null;
      }

      this.error = 'iframe 加载失败，可能是跨域问题或目标服务器不可访问';
      this.connectionStatus = 'error';
      this.loading = false;
    },

    injectAuthToIframe() {
      try {
        console.log('尝试注入认证信息到iframe...');
        console.log('$refs对象:', this.$refs);

        const iframe = this.$refs.workflowFrame;
        console.log('iframe元素:', iframe);
        console.log('iframe类型:', typeof iframe);

        if (!iframe) {
          console.error('iframe元素未找到！检查ref是否正确设置');
          console.log('可用的refs:', Object.keys(this.$refs));
          return;
        }

        console.log('iframe src:', iframe.src);
        console.log('iframe contentWindow:', iframe.contentWindow);

        if (iframe.contentWindow) {
          console.log('iframe contentWindow可访问，尝试注入脚本...');

          // 等待iframe完全加载
          setTimeout(() => {
            try {
              // 简化的认证信息注入
              const message = `eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiIzdXYxdjJ2NWN1cmlQTUhiaHJTSnJTbEFWSG5adEFLOCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuawtOWKoeWxgCIsImRlcHRDYXRlZ29yeSI6IiJ9.cFKY3lk1yIhjYuKV2ssT6096vieNBzHNSZMCeXdVJzg`;

              alert('尝试向iframe发送消息:');
              iframe.contentWindow.postMessage(message, '*');
            } catch (evalError) {
              console.warn('脚本注入失败 (跨域限制):', evalError);
            }
          }, 1000);
        } else {
          console.warn('无法访问iframe contentWindow，可能是跨域限制');
        }
      } catch (error) {
        console.warn('无法注入认证信息到iframe:', error);
        console.log('这通常是由于跨域安全限制导致的');
      }
    },

    retryConnection() {
      this.initializeWorkflow();
    },

    async testConnection() {
      try {
        console.log('测试连接到工作流系统...');
        const response = await fetch(this.baseUrl, {
          method: 'HEAD',
          mode: 'no-cors',
          headers: {
            'authorization': `Bearer ${this.authToken}`,
            'clientid': this.clientId
          }
        });
        console.log('连接测试完成，状态:', response.status);
        this.$message.success('连接测试完成，请查看控制台日志');
      } catch (error) {
        console.error('连接测试失败:', error);
        this.$message.error('连接测试失败: ' + error.message);
      }
    },

    getStatusText() {
      switch (this.connectionStatus) {
        case 'connecting':
          return '正在连接...';
        case 'connected':
          return '已连接';
        case 'error':
          return '连接失败';
        default:
          return '未知状态';
      }
    }
  }
}
</script>

<style scoped>
.workflow-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background-color: #f5f5f5;
}

.header-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-info h2 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
}

.status-indicator.connecting {
  background-color: #ffa500;
  animation: pulse 1.5s infinite;
}

.status-indicator.connected {
  background-color: #4caf50;
}

.status-indicator.error {
  background-color: #f44336;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.status-text {
  font-size: 14px;
  color: #666;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 80px);
}

.error-container {
  padding: 20px;
  text-align: center;
}

.auth-info {
  position: absolute;
  top: 80px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
}

.system-watermark {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
  pointer-events: none;
}

.debug-info {
  position: absolute;
  top: 80px;
  left: 20px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
  max-width: 400px;
}

.debug-info p {
  margin: 5px 0;
  word-break: break-all;
}

.embedded-frame {
  width: 100%;
  height: calc(100vh - 80px);
  border: none;
  background-color: white;
}
</style>
