/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.user;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.util.concurrent.ListenableFuture;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.*;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.CustomerId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.common.data.page.TextPageData;
import org.thingsboard.server.common.data.page.TextPageLink;
import org.thingsboard.server.common.data.security.Authority;
import org.thingsboard.server.common.data.security.UserCredentials;
import org.thingsboard.server.dao.DaoUtil;
import org.thingsboard.server.dao.customer.CustomerDao;
import org.thingsboard.server.dao.entity.AbstractEntityService;
import org.thingsboard.server.dao.exception.DataValidationException;
import org.thingsboard.server.dao.exception.IncorrectParameterException;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.model.sql.UserEntity;
import org.thingsboard.server.dao.model.sql.UserTenant;
import org.thingsboard.server.dao.model.sql.UserWithPassword;
import org.thingsboard.server.dao.model.sql.department.Department;
import org.thingsboard.server.dao.model.sql.department.Organization;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderRoleAuthRelation;
import org.thingsboard.server.dao.project.ProjectRelationService;
import org.thingsboard.server.dao.rabbitMQ.SyncUserMQ;
import org.thingsboard.server.dao.role.RoleService;
import org.thingsboard.server.dao.service.DataValidator;
import org.thingsboard.server.dao.service.PaginatedRemover;
import org.thingsboard.server.dao.sql.department.DepartmentMapper;
import org.thingsboard.server.dao.sql.department.OrganizationMapper;
import org.thingsboard.server.dao.sql.user.UserMapper;
import org.thingsboard.server.dao.sql.user.UserTenantRepository;
import org.thingsboard.server.dao.sql.user.UserWithPasswordMapper;
import org.thingsboard.server.dao.sql.workOrder.WorkOrderRoleAuthRelationMapper;
import org.thingsboard.server.dao.tenant.TenantDao;

import java.util.*;
import java.util.stream.Collectors;

import static org.thingsboard.server.dao.service.Validator.*;

@Service
@Slf4j
public class UserServiceImpl extends AbstractEntityService implements UserService {

    private static final int DEFAULT_TOKEN_LENGTH = 30;
    public static final String INCORRECT_USER_ID = "Incorrect userId ";
    public static final String INCORRECT_TENANT_ID = "Incorrect tenantId ";

    @Value("${security.user_login_case_sensitive:true}")
    private boolean userLoginCaseSensitive;

    @Autowired
    private UserDao userDao;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserWithPasswordMapper userWithPasswordMapper;

    @Autowired
    private UserCredentialsDao userCredentialsDao;

    @Autowired
    private TenantDao tenantDao;

    @Autowired
    private CustomerDao customerDao;

    @Autowired
    private ProjectRelationService projectRelationService;

    @Autowired
    private UserTenantRepository userTenantRepository;

    @Autowired
    private OrganizationMapper organizationMapper;

    @Autowired
    private DepartmentMapper departmentMapper;

    @Autowired
    private WorkOrderRoleAuthRelationMapper workOrderRoleAuthRelationMapper;

    @Autowired
    private RoleService roleService;

    @Autowired
    private SyncUserMQ syncUserMQ;


    @Override
    public User findUserByEmail(TenantId tenantId, String email) {
        log.trace("Executing findUserByEmail [{}]", email);
        validateString(email, "Incorrect email " + email);
        if (userLoginCaseSensitive) {
            return userDao.findByEmail(tenantId, email);
        } else {
            return userDao.findByEmail(tenantId, email.toLowerCase());
        }
    }

    @Override
    public User findUserByEmail(String email) {
        if (userLoginCaseSensitive) {
            return userDao.findByEmail(email);
        } else {
            return userDao.findByEmail(email.toLowerCase());
        }
    }

    @Override
    public User findUserById(TenantId tenantId, UserId userId) {
        log.trace("Executing findUserById [{}]", userId);
        validateId(userId, INCORRECT_USER_ID + userId);
        return userDao.findById(tenantId, userId.getId());
    }

    @Override
    public User findUserById(UserId userId) {
        User user = userDao.findById(userId.getId());
        User result = DaoUtil.getData(userMapper.findByUserId(UUIDConverter.fromTimeUUID(userId.getId())));
        if (user != null && result != null) {
            result.setAdditionalInfo(user.getAdditionalInfo());
        }
        return result;
    }

    @Override
    public ListenableFuture<User> findUserByIdAsync(TenantId tenantId, UserId userId) {
        log.trace("Executing findUserByIdAsync [{}]", userId);
        validateId(userId, INCORRECT_USER_ID + userId);
        return userDao.findByIdAsync(tenantId, userId.getId());
    }

    @Override
    @Transactional
    public User saveUser(User user) throws ThingsboardException {
        log.trace("Executing saveUser [{}]", user);
        userValidator.validate(user, User::getTenantId);
        if (user.getId() == null && !userLoginCaseSensitive) {
            user.setEmail(user.getEmail().toLowerCase());
        }
        if (StringUtils.isNotBlank(user.getSerialNo())) {
            User userBySerialNo = userDao.findBySerialNo(user.getSerialNo());
            if (userBySerialNo != null) {
                throw new ThingsboardException("IC卡号已被绑定!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
            }
        }
        if (StringUtils.isNotBlank(user.getLoginName())) {
            User userByLoginName = userDao.findByEmail(user.getLoginName());
            if (userByLoginName != null) {
                throw new ThingsboardException("工号已存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
            }
        }
        // 检验邮箱、手机号、姓名是否已存在
        List<User> users = userDao.findByExist(user.getFirstName(), user.getPhone(), user.getEmail());
        if (user.getId() == null) {
            if (users != null && users.size() > 0) {
                throw new ThingsboardException("用户姓名、手机号、邮箱地址已存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
            }
        } else {
            for (User existUser : users) {
                if (!existUser.getId().equals(user.getId())) {
                    throw new ThingsboardException("用户姓名、手机号、邮箱地址已存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
                }
            }
        }
        User savedUser = userDao.save(user.getTenantId(), user);
        UserCredentials userCredentials = null;
        if (user.getId() == null) {
            userCredentials = new UserCredentials();
        } else {
            userCredentials = userCredentialsDao.findByUserId(user.getUuidId());
        }
        userCredentials.setEnabled(true);
        userCredentials.setUserId(new UserId(savedUser.getUuidId()));
        if (StringUtils.isNoneBlank(user.getPassword())) {
            userCredentials.setPassword(user.getPassword());
        }
        User mqUser = new User();
        BeanUtils.copyProperties(savedUser, mqUser);
        mqUser.setPassword(user.getPassword());
        syncUserMQ.sendAddUser(JSON.toJSONString(savedUser));
        userCredentialsDao.save(user.getTenantId(), userCredentials);
        return savedUser;
    }

    @Override
    public UserCredentials findUserCredentialsByUserId(TenantId tenantId, UserId userId) {
        log.trace("Executing findUserCredentialsByUserId [{}]", userId);
        validateId(userId, INCORRECT_USER_ID + userId);
        return userCredentialsDao.findByUserId(tenantId, userId.getId());
    }

    @Override
    public UserCredentials findUserCredentialsByUserId(UserId userId) {
        return userCredentialsDao.findUserCredentialsByUserId(userId);
    }

    @Override
    public UserCredentials findUserCredentialsByActivateToken(TenantId tenantId, String activateToken) {
        log.trace("Executing findUserCredentialsByActivateToken [{}]", activateToken);
        validateString(activateToken, "Incorrect activateToken " + activateToken);
        return userCredentialsDao.findByActivateToken(tenantId, activateToken);
    }

    @Override
    public UserCredentials findUserCredentialsByResetToken(TenantId tenantId, String resetToken) {
        log.trace("Executing findUserCredentialsByResetToken [{}]", resetToken);
        validateString(resetToken, "Incorrect resetToken " + resetToken);
        return userCredentialsDao.findByResetToken(tenantId, resetToken);
    }

    @Override
    public UserCredentials saveUserCredentials(TenantId tenantId, UserCredentials userCredentials) {
        log.trace("Executing saveUserCredentials [{}]", userCredentials);
        userCredentialsValidator.validate(userCredentials, data -> tenantId);
        return userCredentialsDao.save(tenantId, userCredentials);
    }

    @Override
    public UserCredentials activateUserCredentials(TenantId tenantId, String activateToken, String password) {
        log.trace("Executing activateUserCredentials activateToken [{}], password [{}]", activateToken, password);
        validateString(activateToken, "Incorrect activateToken " + activateToken);
        validateString(password, "Incorrect password " + password);
        UserCredentials userCredentials = userCredentialsDao.findByActivateToken(tenantId, activateToken);
        if (userCredentials == null) {
            throw new IncorrectParameterException(String.format("Unable to find user credentials by activateToken [%s]", activateToken));
        }
        if (userCredentials.isEnabled()) {
            throw new IncorrectParameterException("User credentials already activated");
        }
        userCredentials.setEnabled(true);
        userCredentials.setActivateToken(null);
        userCredentials.setPassword(password);

        return saveUserCredentials(tenantId, userCredentials);
    }

    @Override
    public UserCredentials requestPasswordReset(TenantId tenantId, String email) {
        log.trace("Executing requestPasswordReset email [{}]", email);
        validateString(email, "Incorrect email " + email);
        User user = userDao.findByEmail(tenantId, email);
        if (user == null) {
            throw new IncorrectParameterException(String.format("Unable to find user by email [%s]", email));
        }
        UserCredentials userCredentials = userCredentialsDao.findByUserId(tenantId, user.getUuidId());
        if (!userCredentials.isEnabled()) {
            throw new IncorrectParameterException("Unable to reset password for inactive user");
        }
        userCredentials.setResetToken(RandomStringUtils.randomAlphanumeric(DEFAULT_TOKEN_LENGTH));
        return saveUserCredentials(tenantId, userCredentials);
    }


    @Override
    public void deleteUser(TenantId tenantId, UserId userId) {
        log.trace("Executing deleteUser [{}]", userId);
        validateId(userId, INCORRECT_USER_ID + userId);
        UserCredentials userCredentials = userCredentialsDao.findByUserId(tenantId, userId.getId());
        userCredentialsDao.removeById(userCredentials.getUuidId());
        deleteEntityRelations(tenantId, userId);
        syncUserMQ.sendDeleteUser(JSON.toJSONString(userId));
        userDao.removeById(userId.getId());
    }

    @Override
    public TextPageData<User> findTenantAdmins(TenantId tenantId, TextPageLink pageLink) {
        log.trace("Executing findTenantAdmins, tenantId [{}], pageLink [{}]", tenantId, pageLink);
        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        validatePageLink(pageLink, "Incorrect page link " + pageLink);
        List<User> users = userDao.findTenantAdmins(tenantId.getId(), pageLink);
        return new TextPageData<>(users, pageLink);
    }

    @Override
    public void deleteTenantAdmins(TenantId tenantId) {
        log.trace("Executing deleteTenantAdmins, tenantId [{}]", tenantId);
        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        tenantAdminsRemover.removeEntities(tenantId, tenantId);
    }

    @Override
    public TextPageData<User> findCustomerUsers(TenantId tenantId, CustomerId customerId, TextPageLink pageLink) {
        log.trace("Executing findCustomerUsers, tenantId [{}], customerId [{}], pageLink [{}]", tenantId, customerId, pageLink);
        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        validateId(customerId, "Incorrect customerId " + customerId);
        validatePageLink(pageLink, "Incorrect page link " + pageLink);
        List<User> users = userDao.findCustomerUsers(tenantId.getId(), customerId.getId(), pageLink);
        return new TextPageData<>(users, pageLink);
    }

    @Override
    public void deleteCustomerUsers(TenantId tenantId, CustomerId customerId) {
        log.trace("Executing deleteCustomerUsers, customerId [{}]", customerId);
        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        validateId(customerId, "Incorrect customerId " + customerId);
        customerUsersRemover.removeEntities(tenantId, customerId);
    }

    private DataValidator<User> userValidator =
            new DataValidator<User>() {
                @Override
                protected void validateDataImpl(TenantId requestTenantId, User user) {
                    /*if (StringUtils.isEmpty(user.getEmail())) {
                        throw new DataValidationException("User email should be specified!");
                    }

                    validateEmail(user.getEmail());*/

                    Authority authority = user.getAuthority();
                    if (authority == null) {
                        throw new DataValidationException("User authority isn't defined!");
                    }
                    TenantId tenantId = user.getTenantId();
                    if (tenantId == null) {
                        tenantId = new TenantId(ModelConstants.NULL_UUID);
                        user.setTenantId(tenantId);
                    }
                    CustomerId customerId = user.getCustomerId();
                    if (customerId == null) {
                        customerId = new CustomerId(ModelConstants.NULL_UUID);
                        user.setCustomerId(customerId);
                    }

                    switch (authority) {
                        case SYS_ADMIN:
                            if (!tenantId.getId().equals(ModelConstants.NULL_UUID)
                                    || !customerId.getId().equals(ModelConstants.NULL_UUID)) {
                                throw new DataValidationException("System administrator can't be assigned neither to tenant nor to customer!");
                            }
                            break;
                        default:
                            break;
                    }
                    if (StringUtils.isNotBlank(user.getEmail())) {
                        User existentUserWithEmail = findUserByEmail(tenantId, user.getEmail());
                        if (existentUserWithEmail != null && !isSameData(existentUserWithEmail, user)) {
                            throw new DataValidationException(user.getEmail() + "已经被使用！");
                        }
                    }
                    if (!tenantId.getId().equals(ModelConstants.NULL_UUID)) {
                        Tenant tenant = tenantDao.findById(tenantId, user.getTenantId().getId());
                        if (tenant == null) {
                            throw new DataValidationException("User is referencing to non-existent tenant!");
                        }
                    }
                    if (!customerId.getId().equals(ModelConstants.NULL_UUID)) {
                        Customer customer = customerDao.findById(tenantId, user.getCustomerId().getId());
                        if (customer == null) {
                            throw new DataValidationException("User is referencing to non-existent customer!");
                        }
                        /* TODO 测试
                        由于我们舍弃了customer这个概念，使用的customer为固定的一个，所以不需要进行下方这个判断
                        else if (!customer.getTenantId().getId().equals(tenantId.getId())) {
                            throw new DataValidationException("User can't be assigned to customer from different tenant!");
                        }*/
                    }
                }
            };

    private DataValidator<UserCredentials> userCredentialsValidator =
            new DataValidator<UserCredentials>() {

                @Override
                protected void validateCreate(TenantId tenantId, UserCredentials userCredentials) {
                    throw new IncorrectParameterException("Creation of new user credentials is prohibited.");
                }

                @Override
                protected void validateDataImpl(TenantId tenantId, UserCredentials userCredentials) {
                    if (userCredentials.getUserId() == null) {
                        throw new DataValidationException("User credentials should be assigned to user!");
                    }
                    if (userCredentials.isEnabled()) {
                        if (StringUtils.isEmpty(userCredentials.getPassword())) {
                            throw new DataValidationException("Enabled user credentials should have password!");
                        }
                        if (StringUtils.isNotEmpty(userCredentials.getActivateToken())) {
                            throw new DataValidationException("Enabled user credentials can't have activate token!");
                        }
                    }
                    UserCredentials existingUserCredentialsEntity = userCredentialsDao.findById(tenantId, userCredentials.getId().getId());
                    if (existingUserCredentialsEntity == null) {
                        throw new DataValidationException("Unable to update non-existent user credentials!");
                    }
                    User user = findUserById(tenantId, userCredentials.getUserId());
                    if (user == null) {
                        throw new DataValidationException("Can't assign user credentials to non-existent user!");
                    }
                }
            };

    private PaginatedRemover<TenantId, User> tenantAdminsRemover = new PaginatedRemover<TenantId, User>() {
        @Override
        protected List<User> findEntities(TenantId tenantId, TenantId id, TextPageLink pageLink) {
            return userDao.findTenantAdmins(id.getId(), pageLink);
        }

        @Override
        protected void removeEntity(TenantId tenantId, User entity) {
            deleteUser(tenantId, new UserId(entity.getUuidId()));
        }
    };

    private PaginatedRemover<CustomerId, User> customerUsersRemover = new PaginatedRemover<CustomerId, User>() {
        @Override
        protected List<User> findEntities(TenantId tenantId, CustomerId id, TextPageLink pageLink) {
            return userDao.findCustomerUsers(tenantId.getId(), id.getId(), pageLink);

        }

        @Override
        protected void removeEntity(TenantId tenantId, User entity) {
            deleteUser(tenantId, new UserId(entity.getUuidId()));
        }
    };

    @Override
    public List<User> findUserByTenant(TenantId tenantId) {
        return userDao.findUserByTenant(tenantId);
    }

    @Override
    public TextPageData<User> findTenantUsers(TenantId tenantId, TextPageLink pageLink) {
        log.trace("Executing findTenatUsers, tenantId [{}],  pageLink [{}]", tenantId, pageLink);
//        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        validatePageLink(pageLink, "Incorrect page link " + pageLink);
        List<User> users = userDao.findTenantUsers(tenantId.getId(), pageLink);

        // 查询用户状态
        List<String> userIds = users.stream().map(user -> UUIDConverter.fromTimeUUID(user.getUuidId())).collect(Collectors.toList());
        List<UserCredentials> userCredentials = userCredentialsDao.findByUserIdList(userIds);
        Map<String, UserCredentials> userCredentialsMap = new HashMap<>();
        userCredentials.forEach(credentials -> userCredentialsMap.put(credentials.getUserId().getId().toString(), credentials));

        for (User user : users) {
            UserCredentials credentials = userCredentialsMap.get(user.getUuidId().toString());
            if (credentials != null) {
                user.setStatus(!credentials.isEnabled());
            }
        }

        return new TextPageData<>(users, pageLink);
    }

    @Override
    public TextPageData<User> findTenantSysAndAdmin(TenantId tenantId, TextPageLink pageLink) {
        log.trace("Executing findTenatAdminAndSYS, tenantId [{}],  pageLink [{}]", tenantId, pageLink);
        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        validatePageLink(pageLink, "Incorrect page link " + pageLink);
        List<Authority> authorityList = new ArrayList<>();
        authorityList.add(Authority.TENANT_SYS);
        authorityList.add(Authority.TENANT_ADMIN);
        List<User> users = userDao.findUsersByAuthorityList(tenantId.getId(), authorityList, pageLink);
        return new TextPageData<>(users, pageLink);

    }

    @Override
    public void assignUserToProjects(List<String> projectIds, String userId) {
        // 删除已有关联
        projectRelationService.mountEntityToProject(DataConstants.ProjectRelationEntityType.USER.name(),
                "", Collections.singletonList(userId));

        // 保存项目用户关系
        projectRelationService.mountEntityToProject(DataConstants.ProjectRelationEntityType.USER.name(),
                projectIds, Collections.singletonList(userId));
    }

    @Override
    public long getAllUserCount() {
        return userDao.find().size();
    }

    @Override
    public List<User> findAll() {
        return userDao.find();
    }

    @Override
    public void changeStatus(String userId, String f) {
        User user = userDao.findById(UUIDConverter.fromString(userId));
        if (user != null) {
            UserCredentials userCredentialsDaoByUserId = userCredentialsDao.findByUserId(user.getUuidId());
            if (userCredentialsDaoByUserId != null) {
                if (f.equalsIgnoreCase("f")) {
                    userCredentialsDaoByUserId.setEnabled(false);
                } else if (f.equalsIgnoreCase("t")) {
                    userCredentialsDaoByUserId.setEnabled(true);
                } else {
                    return;
                }
                userCredentialsDao.save(userCredentialsDaoByUserId);
            }
        }
    }

    @Override
    public List<Tenant> findUserTenantByUserId(String userId) {
        List<UserTenant> list = userTenantRepository.findByUserId(userId);
        List<String> tenantIdList = list.stream().map(UserTenant::getTenantId).collect(Collectors.toList());

        List<Tenant> tenantList = tenantDao.findByIdIn(tenantIdList);

        return tenantList;
    }

    @Override
    public PageData<User> findList(Integer page, Integer size, String title, String authority, String tenantId) {
        PageRequest pageRequest = new PageRequest(page - 1, size, Sort.Direction.DESC, "id");

        PageData<User> list = userDao.findList(title, authority, tenantId, pageRequest);
        List<User> users = list.getData();

        // 查询用户状态
        List<String> userIds = users.stream().map(user -> UUIDConverter.fromTimeUUID(user.getUuidId())).collect(Collectors.toList());
        List<UserCredentials> userCredentials = userCredentialsDao.findByUserIdList(userIds);
        Map<String, UserCredentials> userCredentialsMap = new HashMap<>();
        userCredentials.forEach(credentials -> userCredentialsMap.put(credentials.getUserId().getId().toString(), credentials));

        for (User user : users) {
            UserCredentials credentials = userCredentialsMap.get(user.getUuidId().toString());
            if (credentials != null) {
                user.setStatus(credentials.isEnabled());
            }
        }

        return list;
    }

    @Override
    public User findUserBySerialNo(String serialNo) {
        return userDao.findBySerialNo(serialNo);
    }

    @Override
    public List<User> findByIdIn(List<String> ids) {
        return userDao.findByIdIn(ids);
    }

    @Override
    public void updateUser(User user) {
        syncUserMQ.sendUpdateUser(JSON.toJSONString(user));
        userDao.save(user);
    }

    @Override
    public PageData<User> findAllByPid(String pid, String name, String roleId, String tenantId, Boolean status, int page, int size) {
        Organization organization = null;
        if (StringUtils.isNotBlank(pid)) {
            organization = organizationMapper.findFirstByIdLike(pid);
        }
        List<String> deptIdList = new ArrayList<>();
        deptIdList.add("-");
        // 查组织
        if (organization != null) {
            // 查找
            this.buildDeptIdList(organization, tenantId, deptIdList);
        } else { // 部门
            deptIdList.add(pid);
        }

        List<User> users = DaoUtil.convertDataList(userMapper.findByDepartmentIdIn(deptIdList, name, roleId, status, page, size, tenantId));

        int total = userMapper.findByDepartmentIdInCount(deptIdList, name, roleId, status, tenantId);

        PageData pageData = new PageData(total, users);

        return pageData;
    }

    @Override
    public List getAllByPidStr(String pid, String tenantId) {
        if (StringUtils.isBlank(pid)) {
            pid = "-";
        }

        List<User> users = DaoUtil.convertDataList(userMapper.findByPidStr(pid, tenantId));

        return users;
    }

    @Override
    public List getAllUser(String name, String tenantId) {
        return DaoUtil.convertDataList(userMapper.selectAll(name, tenantId));
    }

    @Override
    public User findByAccountKey(String accountKey) {
        List<User> userList = userDao.findByExist(accountKey, accountKey, accountKey);
        if (userList != null && userList.size() > 0) {
            return userList.get(0);
        }
        return null;
    }

    @Override
    public List<User> findListByAuth(String authType, User currentUser) {
        if (Authority.TENANT_ADMIN.equals(currentUser.getAuthority())) {
            PageRequest pageRequest = new PageRequest(0, 9999, Sort.Direction.DESC, "id");

            PageData<User> list = userDao.findList("", Authority.CUSTOMER_USER.name(), UUIDConverter.fromTimeUUID(currentUser.getTenantId().getId()), pageRequest);
            List<User> users = list.getData();
            return users;
        }
        // 查询当前用户的角色
        String roleId = roleService.getRoleIdByUserId(currentUser.getId());

        // 查询当前用户角色所有的权限
        QueryWrapper<WorkOrderRoleAuthRelation> roleAuthRelationQueryWrapper = new QueryWrapper<>();
        roleAuthRelationQueryWrapper.eq("auth_type", authType).eq("role_id", roleId);
        List<WorkOrderRoleAuthRelation> relations = workOrderRoleAuthRelationMapper.selectList(roleAuthRelationQueryWrapper);
        List<String> deptIds = relations.stream().map(WorkOrderRoleAuthRelation::getDeptId).collect(Collectors.toList());

        // 查询该用户有的部门权限
        if (deptIds.isEmpty()) {
            // 没有任何权限只能给自己派单
            return Collections.singletonList(currentUser);
        }
        List<User> userList = DaoUtil.convertDataList(userMapper.findByDeptIdIn(deptIds));
        return userList;
    }


    public void buildDeptIdList(Organization organization, String tenantId, List<String> deptIdList) {
        List<Department> departmentList = departmentMapper.findChildren(organization.getId(), tenantId);
        for (Department department : departmentList) {
            this.buildDeptIdList(department, tenantId, deptIdList);
        }
        List<Organization> repositoryChildren = organizationMapper.findChildren(organization.getId(), tenantId);
        for (Organization childOrganization : repositoryChildren) {
            this.buildDeptIdList(childOrganization, tenantId, deptIdList);
        }
    }

    public void buildDeptIdList(Department department, String tenantId, List<String> deptIdList) {
        deptIdList.add(department.getId());
        List<Department> childrenList = departmentMapper.findChildren(department.getId(), tenantId);
        for (Department child : childrenList) {
            this.buildDeptIdList(child, tenantId, deptIdList);
        }
    }


    @Override
    public List<UserWithPassword> getAllUsers() {
        //TODO 2025/6/5 连表查询，需要用户和密码
        List<UserWithPassword> allWithPassword = userWithPasswordMapper.getAllWithPassword();
        return allWithPassword;
    }
}
