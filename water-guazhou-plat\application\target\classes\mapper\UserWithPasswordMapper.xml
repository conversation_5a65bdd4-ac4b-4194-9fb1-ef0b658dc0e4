<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.user.UserWithPasswordMapper">

    <select id="getAllWithPassword" resultType="org.thingsboard.server.dao.model.sql.UserWithPassword">
        select  u.id ,
                        u.additional_info,
                        u.authority,
                        u.customer_id,
                        u.email,
                        u.first_name,
                        u.last_name,
                        u.search_text,
                        u.tenant_id,
                        u.phone,
                        u.login_name,
                        u.serial_no,
                        u.department_id,
                        u.create_time,
                        uc.password
        from tb_user u
        left join user_credentials uc on u.id = uc.user_id
    </select>
</mapper>