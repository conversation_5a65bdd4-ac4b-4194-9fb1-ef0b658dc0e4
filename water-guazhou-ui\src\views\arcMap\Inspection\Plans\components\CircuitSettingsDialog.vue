<template>
  <el-dialog
    v-model="visible"
    :title="getDialogTitle()"
    width="600px"
    :before-close="handleClose"
  >
    <!-- 数据加载中的骨架屏 -->
    <div v-if="dataLoading" class="loading-skeleton">
      <el-skeleton :rows="4" animated />
    </div>

    <!-- 表单内容 -->
    <el-form
      v-else
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="配置名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入配置名称" :readonly="readonly" />
      </el-form-item>

      <el-form-item label="配置编码" prop="code">
        <el-input v-model="formData.code" placeholder="请输入配置编码" :readonly="readonly" />
      </el-form-item>

      <el-form-item label="配置类型" prop="type">
        <el-select v-model="formData.type" placeholder="请选择配置类型" style="width: 100%" :disabled="readonly">
          <el-option label="管网" value="0" />
          <el-option label="泵站" value="1" />
          <el-option label="其他" value="2" />
        </el-select>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%" :disabled="readonly">
          <el-option label="启用" value="0" />
          <el-option label="停用" value="1" />
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">{{ readonly ? '关闭' : '取消' }}</el-button>
        <el-button v-if="!readonly" type="primary" @click="handleSubmit" :loading="submitLoading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { addCircuitSettings, updateCircuitSettings, getCircuitSettingsById } from '@/api/smartManagement/circuitSettings'

interface Props {
  modelValue: boolean
  editId?: string
  readonly?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  editId: '',
  readonly: false
})

const emit = defineEmits<Emits>()

const visible = ref(false)
const dataLoading = ref(false) // 数据加载状态
const submitLoading = ref(false) // 提交加载状态
const formRef = ref<FormInstance>()
const isEdit = ref(false)

const formData = reactive({
  name: '',
  code: '',
  type: '',
  status: '0'
})

const rules: FormRules = {
  name: [
    { required: true, message: '请输入配置名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入配置编码', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择配置类型', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 监听弹窗显示状态
watch(() => props.modelValue, async (val) => {
  visible.value = val
  if (val) {
    isEdit.value = !!props.editId
    if (isEdit.value) {
      await loadEditData()
    } else {
      resetForm()
    }
  }
})

// 监听弹窗关闭
watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 获取弹窗标题
const getDialogTitle = () => {
  if (props.readonly) {
    return '查看培训配置'
  }
  return isEdit.value ? '编辑培训配置' : '新建培训配置'
}

// 重置表单
const resetForm = () => {
  formData.name = ''
  formData.code = ''
  formData.type = ''
  formData.status = '0'
  // 使用 nextTick 确保 DOM 更新后再清除验证
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 加载编辑数据
const loadEditData = async () => {
  if (!props.editId) return

  try {
    dataLoading.value = true
    const res = await getCircuitSettingsById(props.editId)
    if (res?.data) {
      // 确保数据完整性，避免undefined值
      formData.name = res.data.name || ''
      formData.code = res.data.code || ''
      formData.type = res.data.type || ''
      formData.status = res.data.status || '0'
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  } finally {
    dataLoading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitLoading.value = true

    if (isEdit.value) {
      await updateCircuitSettings(props.editId, formData)
      ElMessage.success('更新成功')
    } else {
      await addCircuitSettings(formData)
      ElMessage.success('创建成功')
    }

    emit('success')
    handleClose()
  } catch (error) {
    console.error(error)
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    submitLoading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  // 重置所有状态
  dataLoading.value = false
  submitLoading.value = false
  resetForm()
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.loading-skeleton {
  padding: 20px 0;
}
</style>
